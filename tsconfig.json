{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "noImplicitAny": false, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["@dcloudio/types"], "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "types/*.d.ts", "src/**/*.vue"], "exclude": ["src/wxcomponents/**/*"]}