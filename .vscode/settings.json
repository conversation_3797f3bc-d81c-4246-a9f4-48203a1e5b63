{"editor.codeLens": true, "search.exclude": {"dist": true, "**/node_modules": true, "package-lock.json": true}, "files.associations": {"pages.json": "jsonc", "manifest.json": "jsonc", "*.nvue": "vue"}, "editor.tabSize": 2, "editor.insertSpaces": true, "html.format.contentUnformatted": "code, pre, textarea", "html.format.indentInnerHtml": true, "html.format.wrapLineLength": 0, "html.format.wrapAttributes": "preserve", "html.format.indentHandlebars": true, "tslint.autoFixOnSave": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "cSpell.words": ["babys", "Consts", "dcloudio", "getphonenumber", "iconfont", "OPPO", "pinia", "qiniu", "refresherrefresh", "refresher<PERSON><PERSON>", "Tpls", "unionid", "usersig", "VITE", "wechat"], "vetur.completion.tagCasing": "initial", "vetur.completion.autoImport": false, "typescript.tsdk": "node_modules/typescript/lib", "px-to-rpx.autoRemovePrefixZero": true, "px-to-rpx.baseWidth": 375, "px-to-rpx.fixedDigits": 1, "i18n-ally.localesPaths": ["src/uni_modules/uni-popup/components/uni-popup/i18n"]}