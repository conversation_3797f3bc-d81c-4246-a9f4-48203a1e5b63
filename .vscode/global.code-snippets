{
  // Place your yousai workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  "自定义指令": {
    "scope": "javascript,typescript",
    "prefix": "log",
    "body": [
      "console.log('打印$1',$1)"
    ],
    "description": "自定义指令 打印test"
  },
  "wh 宽高": {
    "scope": "css,scss,less,stylus",
    "prefix": "wh",
    "body": [
      "width: $1px;",
      "height: $1px;",
    ],
    "description": "自定义指令 打印test"
  },
  "条件判断 APP": {
    "scope": "css,scss,less,stylus,javascript,typescript",
    "prefix": "ifa",
    "body": [
      "/* #ifdef APP-PLUS */",
      "/* #endif */",
    ],
    "description": ""
  },
  "条件判断 ifndef": {
    "scope": "css,scss,less,stylus,javascript,typescript",
    "prefix": "ifn",
    "body": [
      "/* #ifndef MP-WEIXIN */",
      "/* #endif */",
    ],
    "description": ""
  },
  "快捷import": {
    "scope": "javascript,typescript",
    "prefix": "imc",
    "body": [
      "import $1 from '@/components/$1.vue'",
      "$2"
    ]
  },
  "生成函数": {
    "scope": "javascript,typescript",
    "prefix": "fun",
    "body": [
      "const $1 = ()=>{}"
    ]
  },
  "todo": {
    "scope": "javascript,typescript",
    "prefix": "todo",
    "body": [
      "//TODO $1",
      "uni.showToast({ title: 'TODO $1', icon: 'none' })"
    ]
  },
  "数组声明": {
    "scope": "javascript,typescript",
    "prefix": "ap",
    "body": [
      "Array as PropType<$1[]>",
      "$2"
    ]
  }
}