import { defineConfig, loadEnv } from 'vite'

import uni from '@dcloudio/vite-plugin-uni'

let envData, env
if (process.env.UNI_CUSTOM_DEFINE) {
  const { env } = JSON.parse(process.env.UNI_CUSTOM_DEFINE)
  envData = loadEnv(env, './')
  console.log('\x1b[31m%s\x1b[0m', `当前环境 : ${env}\n请求地址 : ${envData.VITE_BASE_URL}`)
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/global.scss";`,
      },
    },
  },
  define: {
    BASE_URL: JSON.stringify(envData.VITE_BASE_URL),
    ENV: JSON.stringify(env),
  },
})
