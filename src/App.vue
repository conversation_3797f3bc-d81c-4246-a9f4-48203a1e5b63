<script lang="ts" setup>
import { onLaunch, onShow } from '@dcloudio/uni-app'

onLaunch(async () => {
  uni.setInnerAudioOption({
    // （仅在 iOS 生效）是否遵循静音开关，设置为 false 之后，即使是在静音模式下，也能播放声音
    obeyMuteSwitch: false,
  })
})

onShow(() => {})
</script>

<style lang="scss">
/*每个页面公共css */
@import '@/uni_modules/uni-scss/index.scss';
/* #ifndef APP-NVUE */

page,
text,
view,
navigator,
swiper,
swiper-item,
scroll-view,
image {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  word-break: break-all;
}
page {
  -webkit-text-size-adjust: none;
}
button {
  &::after {
    display: none;
  }
}
// 隐藏安卓机scroll-view滚动条
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
/* #endif */
</style>
