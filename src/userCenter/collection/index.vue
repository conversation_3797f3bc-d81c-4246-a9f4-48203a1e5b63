<template>
  <z-paging
    ref="paging"
    v-model="dataList"
    refresher-enabled
    bg-color="#ffffff"
    @refresh="onRefresh"
    @query="queryList"
  >
    <view class="list">
      <view class="list-item" v-for="(room, index) in dataList" :key="index">
        <RoomCard :room-data="room" />
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { ref } from 'vue'

import RoomCard from '@/components/room-card/index.vue'

import { getCollectNum } from '@/apis/user'
import { userStore } from '@/store'

const store = userStore()
const { userInfo } = storeToRefs(store)
const paging = ref()
const dataList = ref([])

const queryList = async () => {
  const res = await getCollectNum({
    phone: userInfo.value.phone,
  })
  const list = res?.houseArr || []
  const oList = list.filter((item) => item.id !== '' && item.id != null)
  paging.value.complete(oList)
}

const onRefresh = () => {
  queryList()
}
</script>

<style lang="scss" scoped>
.list {
  width: 100%;
  padding: 20rpx 30rpx 0;
  box-sizing: border-box;
  &-item {
    margin-bottom: 30rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
