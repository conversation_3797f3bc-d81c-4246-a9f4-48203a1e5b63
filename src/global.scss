@use 'sass:math';

//  无需引入，直接使用
// 超出...
@mixin text-overflow($line) {
  // #{$property}: $ltr-value;
  overflow: hidden;
  word-break: break-all; /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis; /* 超出部分省略号 */
  display: -webkit-box; /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical; /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: $line; /** 显示的行数 **/
}

// 输入px,返回rpx
@function rpx($px) {
  @return $px * 2rpx;
}

// 横屏适配 rpx 转 vmin
@function vpx($rpx) {
  //$rpx为需要转换的字号
  @return #{math.div($rpx, 7.5)}vmin;
}
