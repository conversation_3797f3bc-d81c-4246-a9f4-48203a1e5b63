/**
 * 延时函数
 * @param ms 毫秒
 * @returns
 */
export const sleep = (ms = 0) => new Promise((resolve) => setTimeout(resolve, ms))

/**
 * 获取元素的完整尺寸信息
 * @param selector 选择器（支持类名、ID、标签等）
 * @param context 查询上下文，默认为当前页面
 * @returns Promise<DOMRect> 返回元素的完整尺寸信息
 */
export const getElementRect = (
  selector: string,
  context?: any,
): Promise<{
  width: number
  height: number
  top: number
  left: number
  right: number
  bottom: number
}> => {
  return new Promise((resolve, reject) => {
    try {
      const query = uni.createSelectorQuery()

      if (context) {
        query.in(context)
      }

      query
        .select(selector)
        .boundingClientRect((data: any) => {
          if (data) {
            resolve(data)
          } else {
            console.warn(`未找到选择器为 ${selector} 的元素`)
            resolve({
              width: 0,
              height: 0,
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            })
          }
        })
        .exec()
    } catch (error) {
      console.error('获取元素尺寸失败:', error)
      reject(error)
    }
  })
}
