<template>
  <view class="search">
    <image src="@/static/icon/search-icon.png" class="search-icon" />
    <input
      type="text"
      placeholder="请输入小区名称开始找房"
      v-model="searchKeyword"
      @confirm="handleSearch"
      class="search-input"
      confirm-type="search"
    />
    <image v-show="!!searchKeyword" src="@/static/icon/clear-icon.png" class="search-clear" @click="handleClear" />
    <view class="search-btn" @click="handleSearch">搜索</view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  modelValue: string
}>()

const emits = defineEmits(['seach', 'update:modelValue'])

const searchKeyword = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  },
})

const handleSearch = () => {
  emits('seach')
}

const handleClear = () => {
  searchKeyword.value = ''
}
</script>

<style lang="scss" scoped>
.search {
  width: 100%;
  display: flex;
  align-items: center;
  border: 1rpx solid #dbdbdb;
  border-radius: 33rpx;
  height: 66rpx;

  &-icon {
    width: 26rpx;
    height: 26rpx;
    margin-left: 30rpx;
  }

  &-input {
    flex: 1;
    height: 100%;
    font-size: 26rpx;
    color: #000000;
    margin-left: 22rpx;

    &::placeholder {
      color: #999999;
    }
  }

  &-clear {
    width: 28rpx;
    height: 28rpx;
    margin-left: 20rpx;
  }

  &-btn {
    padding: 0 22rpx;
    color: #8cc224;
    font-size: 26rpx;
    border-left: 1rpx solid #dbdbdb;
    margin-left: 20rpx;
  }
}
</style>
