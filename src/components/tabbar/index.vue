<template>
  <view class="tab-bar-block"></view>
  <view class="tab-bar-wrap">
    <view class="tab-bar">
      <view
        class="tab-item"
        v-for="tab in tabList"
        :class="calcOn(tab) ? 'on' : ''"
        :key="tab.pagePath"
        @click="go('/' + tab.pagePath)"
      >
        <image class="tab-img" v-show="!calcOn(tab)" :src="tab.iconPath" />
        <image class="tab-img" v-show="calcOn(tab)" :src="tab.selectedIconPath" />
        <view class="text">{{ tab.text }}</view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { reactive } from 'vue'

import apartmentActiveIcon from '@/static/tabbar/apartment-active.png'
import apartmentIcon from '@/static/tabbar/apartment.png'
import findActiveIcon from '@/static/tabbar/find-active.png'
import findIcon from '@/static/tabbar/find.png'
import homeActiveIcon from '@/static/tabbar/home-active.png'
import homeIcon from '@/static/tabbar/home.png'
import mineActiveIcon from '@/static/tabbar/mine-active.png'
import mineIcon from '@/static/tabbar/mine.png'
import { onHide, onShow } from '@dcloudio/uni-app'

const props = defineProps<{
  test?: string
}>()
const state: {
  currentPath: string
} = reactive({
  currentPath: '/pages/index/index',
})
const go = (url) => {
  uni.switchTab({
    url,
  })
}
const calcOn = (tab) => {
  return state.currentPath.includes(tab.pagePath)
}
onShow((options) => {
  state.currentPath = (getCurrentPages()[0] as any)?.$page?.fullPath || '/pages/index/index'
})
onHide(() => {
  state.currentPath = ''
})
const tabList = [
  {
    pagePath: 'pages/index/index',
    iconPath: homeIcon,
    selectedIconPath: homeActiveIcon,
    text: '首页',
  },
  {
    pagePath: 'pages/apartment/index',
    iconPath: apartmentIcon,
    selectedIconPath: apartmentActiveIcon,
    text: '公寓',
  },
  {
    pagePath: 'pages/find/index',
    iconPath: findIcon,
    selectedIconPath: findActiveIcon,
    text: '找房',
  },
  {
    pagePath: 'pages/mine/index',
    iconPath: mineIcon,
    selectedIconPath: mineActiveIcon,
    text: '我的',
  },
]
</script>
<style lang="scss" scoped>
.tab-bar-block {
  height: calc(50px + env(safe-area-inset-bottom));
}

.tab-bar-wrap {
  width: 100%;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 12;
  padding-bottom: env(safe-area-inset-bottom);
  background: #ffffff;
  box-shadow: 0rpx -1rpx 4rpx 1rpx rgba(0, 0, 0, 0.08);

  .tab-bar {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #ffffff;
    box-sizing: border-box;

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 25%;

      .tab-img {
        width: 44rpx;
        height: 44rpx;
      }

      .text {
        font-size: 22rpx;
        color: #707070;
        display: block;
        margin-top: 8rpx;
      }

      &.on {
        .text {
          color: #000000;
        }
      }
    }
  }
}
</style>
