<template>
  <view class="room-card" @click="handleClick">
    <image :src="roomData.picObj?.big || RoomDefaultImg" class="room-image" mode="aspectFill" />

    <view class="room-info">
      <view class="room-title">{{ roomData.quyuCName }}-{{ roomData.chaoxiang.key }}-{{ roomData.fangjianName }}</view>

      <view class="room-dec">
        {{ roomData.mianji }}㎡<view class="line"></view>{{ roomData.loucengA }}/{{ roomData.loucengB
        }}<view class="line"></view>朝{{ roomData.orientationName }}
      </view>

      <view class="room-location">
        <image class="location-icon" src="@/static/icon/address-icon.png"></image>
        <text class="room-address">{{ roomData.address }}</text>
      </view>

      <view class="room-tag"> 保租房 </view>

      <view class="room-price">
        <text class="price">{{ roomData.zujin }}</text>
        <text class="price-unit">元/月起</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import RoomDefaultImg from '@/static/image/room-default.png'

const props = defineProps<{
  roomData: any
}>()

const handleClick = () => {
  uni.navigateTo({
    url: `/pages/roomDetail/index?id=${props.roomData.id}`,
  })
}
</script>

<style lang="scss" scoped>
.room-card {
  display: flex;
  align-items: center;

  .room-image {
    width: 300rpx;
    height: 200rpx;
    border-radius: 16rpx;
  }

  .room-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20rpx;
    height: 200rpx;

    .room-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #000000;
      width: 100%;
      @include text-overflow(1);
    }

    .room-dec {
      font-size: 22rpx;
      color: #808080;
      display: flex;
      align-items: center;
      .line {
        width: 1rpx;
        height: 24rpx;
        margin: 0 12rpx;
        background: #808080;
      }
    }

    .room-location {
      display: flex;
      align-items: center;
      gap: 10rpx;

      .location-icon {
        width: 22rpx;
        height: 22rpx;
      }

      .room-address {
        font-size: 22rpx;
        color: #808080;
      }
    }

    .room-tag {
      align-self: flex-start;
      background-color: #00a8cf;
      color: #ffffff;
      font-size: 22rpx;
      padding: 8rpx;
      border-radius: 4rpx;
    }

    .room-price {
      display: flex;
      align-items: baseline;
      gap: 5rpx;
      color: #8cc224;

      .price {
        font-size: 32rpx;
        font-weight: bold;
      }

      .price-unit {
        font-size: 24rpx;
      }
    }
  }
}
</style>
