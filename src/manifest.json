{
    "name": "ji<PERSON><PERSON><PERSON>",
    "appid": "__UNI__D19DD44",
    "description": "",
    "versionName": "1.0.0",
    "versionCode": "100",
    "transformPx": false,
    "app-plus": {
        "softinputNavBar": "none",
        /* 5+App特有相关 */
        "softinputMode": "adjustResize",
        "usingComponents": true,
        "nvueCompiler": "uni-app",
        "nvueStyleCompiler": "uni-app",
        "splashscreen": {
            "alwaysShowBeforeRender": true,
            "waiting": true,
            "autoclose": true,
            "delay": 0
        },
        "modules": {
            "Share": {},
            "VideoPlayer": {}
        },
        /* 模块配置 */
        "distribute": {
            /* 应用发布信息 */
            "android": {
                /* android打包配置 */
                "permissions": [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios": {},
            /* ios打包配置 */
            "sdkConfigs": {
                "share": {
                    "weixin": {
                        "appid": "wx53dd0253925b4838",
                        "UniversalLinks": ""
                    }
                }
            }
        }
    },
    /* SDK配置 */
    "quickapp": {},
    /* 快应用特有相关 */
    "mp-weixin": {
        /* 小程序特有相关 */
        "appid": "wx0c39bad82faf0705",
        "resizable": false,
        "setting": {
            "urlCheck": true,
            "es6": true,
            "postcss": true
        },
        "libVersion": "latest",
        "usingComponents": true,
        "permission": {
            "scope.userLocation": {
                "desc": "测试版本"
            }
        },
        "requiredBackgroundModes": [
            "audio"
        ],
        "optimization": {
            "subPackages": true
        }
    },
    "vueVersion": "3"
}