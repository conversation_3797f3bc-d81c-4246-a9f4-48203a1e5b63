import { post } from '@/utils/request'

// 获取验证码
export const getVerifyCode = (data) => {
  return post('/v2/sms/worker/get_code', data)
}

// 登录
export const loginByCode = (data) => {
  return post('/v2/customer/house_user/login_weChat_zuke', data)
}

// 获取用户相关信息
export const getUserHouseInfo = (data) => {
  return post('/v2/web/compact/renter/get_list', data)
}

// 获取收藏数量
export const getCollectNum = (data) => {
  return post('/v2/web/wechat_item/get_collection_list', data)
}

// 获取约看数量
export const getAppointmentNum = (data) => {
  return post('/v2/web/wechat_item/get_customer_list', data)
}

// 获取预定数量
export const getReserveNum = (data) => {
  return post('/v2/web/wechat_item/get_book_list_by_phone', data)
}

// 获取待缴账单列表
export const getPaybill = (data, loadingMsg) => {
  return post('/v2/web/balance/get_renter_list', data, {}, loadingMsg)
}
