<template>
  <z-paging
    ref="paging"
    v-model="dataList"
    refresher-enabled
    bg-color="#ffffff"
    :paging-style="{ height: 'calc(100vh - 50px - env(safe-area-inset-bottom))' }"
    :fixed="false"
    @refresh="onRefresh"
    @query="queryList"
  >
    <template #top>
      <view class="find-page-top">
        <CustomNavbar title="找房" :show-back="false" />
        <view class="find-page-top-box">
          <SearchBar v-model="searchKeyword" @seach="handleSearch" />
          <view class="setion">
            <view class="setion-item" v-for="item in setionList" :key="item.value" @click="chooseSetion(item)">
              <text :class="{ active: item.chooseValue }">{{ item.label }}</text>
              <image v-if="item.isCurrent" class="icon" src="@/static/icon/choose-open.png"></image>
              <image v-else class="icon" src="@/static/icon/choose-close.png"></image>
            </view>
            <view class="change">
              <image class="change-icon" src="@/static/icon/choose-change.png"></image>
            </view>
          </view>
        </view>
        <view class="line"></view>
      </view>
    </template>
    <view class="list">
      <view class="list-item" v-for="(room, index) in dataList" :key="index">
        <RoomCard :room-data="room" />
      </view>
    </view>
    <template slot="empty">
      <view class="empty"> 空 </view>
    </template>
  </z-paging>
  <ChoosePopup ref="choosePopupRef" :top="topHeight" :setionList="setionList" />
  <Tabbar />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'
import RoomCard from '@/components/room-card/index.vue'
import SearchBar from '@/components/search-bar/index.vue'
import Tabbar from '@/components/tabbar/index.vue'

import { getElementRect, sleep } from '@/utils'

import ChoosePopup from './components/ChoosePopup.vue'

const instance = getCurrentInstance()
const topHeight = ref(0)
const choosePopupRef = ref()
const paging = ref()
const dataList = ref()
const searchKeyword = ref('')
const setionList = ref([
  {
    label: '类型',
    value: 'type',
    list: [
      {
        label: '租赁方式',
        children: [
          {
            label: '集中式',
            value: 'jizhongshi',
          },
        ],
      },
      {
        label: '房源类型',
        children: [
          {
            label: '保租房',
            value: 'baozufang',
          },
          {
            label: '市场化租赁房',
            value: 'shichang',
          },
        ],
      },
    ],
    isCurrent: false,
    chooseValue: [],
  },
  {
    label: '位置',
    value: 'location',
    list: [
      {
        label: '黄浦区',
        value: 'huangpu',
      },
      {
        label: '徐汇区',
        value: 'xuhui',
      },
      {
        label: '长宁区',
        value: 'changning',
      },
      {
        label: '静安区',
        value: 'jingan',
      },
    ],
    isCurrent: false,
    chooseValue: [],
  },
  {
    label: '房型',
    value: 'roomType',
    list: [
      {
        label: '房型',
        children: [
          {
            label: '一居室',
            value: 'yijushi',
          },
          {
            label: '二居室',
            value: 'erjushi',
          },
          {
            label: '三层及以上',
            value: 'sanceng',
          },
        ],
      },
      {
        label: '朝向',
        children: [
          {
            label: '朝南',
            value: 'nan',
          },
          {
            label: '朝北',
            value: 'bei',
          },
          {
            label: '朝西',
            value: 'xi',
          },
          {
            label: '朝东',
            value: 'dong',
          },
          {
            label: '东南',
            value: 'dongnan',
          },
          {
            label: '东北',
            value: 'dongbei',
          },
          {
            label: '西南',
            value: 'xinan',
          },
          {
            label: '西北',
            value: 'xibei',
          },
        ],
      },
      {
        label: '装修',
        children: [
          {
            label: '装修1',
            value: 'zhuangxiu1',
          },
          {
            label: '装修2',
            value: 'zhuangxiu2',
          },
        ],
      },
      {
        label: '特色',
        children: [
          {
            label: '特色1',
            value: 'tese1',
          },
          {
            label: '特色2',
            value: 'tese2',
          },
        ],
      },
    ],
    isCurrent: false,
    chooseValue: [],
  },
  {
    label: '租金',
    value: 'price',
    list: [
      {
        label: '1500元以下',
        value: '1500',
      },
      {
        label: '1500~2000元',
        value: '2000',
      },
      {
        label: '2001~3000元',
        value: '3000',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
])

const getNavTopHeight = async () => {
  await sleep()
  const res = await getElementRect('.find-page-top', instance)
  topHeight.value = res.height
}

const chooseSetion = (item) => {
  if (item.isCurrent) {
    item.isCurrent = false
    choosePopupRef.value.close()
    return
  }

  setionList.value.forEach((v) => {
    v.isCurrent = v.value === item.value
  })
  choosePopupRef.value.open()
}

const handleSearch = () => {}

const onRefresh = () => {}

const queryList = () => {
  setTimeout(() => {
    paging.value.complete([
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
    ])
  }, 200)
}

onMounted(() => {
  getNavTopHeight()
})
</script>

<style lang="scss" scoped>
.find-page-top {
  position: relative;
  z-index: 100;
  background: #ffffff;
  &-box {
    padding: 0 30rpx;
    .setion {
      width: 100%;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 26rpx;
          color: #333333;
          &.active {
            color: #8cc224;
          }
        }
        .icon {
          width: 18rpx;
          height: 18rpx;
          margin-left: 10rpx;
        }
      }
      .change {
        width: 36rpx;
        height: 36rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0, 0, 0, 0.16);
        border-radius: 50%;
        &-icon {
          width: 22rpx;
          height: 22rpx;
        }
      }
    }
  }
  .line {
    width: 100%;
    height: 10rpx;
    background: #f8f8f8;
  }
}
.list {
  width: 100%;
  padding: 20rpx 30rpx 0;
  box-sizing: border-box;
  &-item {
    margin-bottom: 30rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
