<template>
  <view class="room-detail-page">
    <!-- 房屋图片轮播 -->
    <view class="room-images">
      <swiper
        class="image-swiper"
        :indicator-dots="true"
        autoplay
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ffffff"
      >
        <swiper-item v-for="(image, index) in roomData.images" :key="index">
          <image :src="image" class="room-image" mode="aspectFill" @click="previewImage(image)" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 房屋基本信息 -->
    <view class="room-info">
      <view class="room-title">{{ roomData.title }}</view>
      <view class="room-details">
        <text class="detail-item">{{ roomData.area }}㎡</text>
        <text class="detail-separator">|</text>
        <text class="detail-item">{{ roomData.floor }}/{{ roomData.totalFloor }}层</text>
        <text class="detail-separator">|</text>
        <text class="detail-item">{{ roomData.orientation }}</text>
      </view>
      <view class="room-price">
        <text class="price">{{ roomData.price }}</text>
        <text class="price-unit">元/月起</text>
        <text class="price-note">（以实际签约为准）</text>
      </view>
      <view class="room-address">
        <image class="location-icon" src="@/static/icon/address-icon.png" />
        <text class="address-text">{{ roomData.address }}</text>
      </view>
    </view>

    <!-- 房源设施 -->
    <view class="facilities-section">
      <view class="section-title">房源设施</view>
      <view class="facilities-grid">
        <view v-for="facility in facilities" :key="facility.name" class="facility-item">
          <image :src="facility.icon" class="facility-icon" />
          <text class="facility-name">{{ facility.name }}</text>
        </view>
      </view>
    </view>

    <!-- 房源描述 -->
    <view class="description-section">
      <view class="section-title">房源描述</view>

      <!-- 房源优势 -->
      <view class="description-item">
        <view class="description-subtitle">一、房源优势</view>
        <view class="description-list">
          <view v-for="(advantage, index) in roomData.advantages" :key="index" class="description-point">
            {{ index + 1 }}、{{ advantage }}
          </view>
        </view>
      </view>

      <!-- 户型介绍 -->
      <view class="description-item">
        <view class="description-subtitle">二、户型介绍</view>
        <view class="description-content">{{ roomData.layoutDescription }}</view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-item" @click="toggleFavorite">
          <image v-if="isFavorite" src="@/static/icon/heart-filled.png" class="action-icon" />
          <image v-else src="@/static/icon/heart-outline.png" class="action-icon" />
          <text class="action-text">收藏</text>
        </view>
        <view class="action-item" @click="shareRoom">
          <image src="@/static/icon/share-icon.png" class="action-icon" />
          <text class="action-text">分享</text>
        </view>
      </view>
      <view class="reserve-btn" @click="reserveRoom"> 预约看房 </view>
    </view>

    <image class="phone-btn" src="@/static/icon/phone-icon.png"></image>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

// 房屋数据接口
interface RoomData {
  id: number
  title: string
  area: string
  floor: number
  totalFloor: number
  orientation: string
  price: string
  address: string
  images: string[]
  advantages: string[]
  layoutDescription: string
}

// 设施接口
interface Facility {
  name: string
  icon: string
}

// 响应式数据
const roomData = ref<RoomData>({
  id: 1,
  title: '整租·菊园社区三居室',
  area: '72',
  floor: 2,
  totalFloor: 19,
  orientation: '朝南',
  price: '1550',
  address: '上海市嘉定区祁连路1255号',
  images: [
    'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
  ],
  advantages: ['满满国际户型，2房全明格局', '客厅宽敞大气', '装修状况：中装修提包入住'],
  layoutDescription: '户型：2室2厅1卫，南北通透房为正使用率高达90%',
})

const isFavorite = ref(false)

// 设施列表
const facilities = ref<Facility[]>([
  { name: '洗衣机', icon: '@/static/icon/washing-machine.png' },
  { name: '空调', icon: '@/static/icon/air-conditioner.png' },
  { name: '衣柜', icon: '@/static/icon/wardrobe.png' },
  { name: '电视', icon: '@/static/icon/tv.png' },
  { name: '冰箱', icon: '@/static/icon/refrigerator.png' },
  { name: '热水器', icon: '@/static/icon/water-heater.png' },
  { name: '床', icon: '@/static/icon/bed.png' },
  { name: '暖气', icon: '@/static/icon/heating.png' },
  { name: '微波炉', icon: '@/static/icon/microwave.png' },
  { name: '宽带', icon: '@/static/icon/wifi.png' },
])

// 分享房源
const shareRoom = () => {
  uni.showActionSheet({
    itemList: ['微信好友', '朋友圈', '复制链接'],
    success: (res) => {
      const actions = ['微信好友', '朋友圈', '复制链接']
      uni.showToast({
        title: `分享到${actions[res.tapIndex]}`,
        icon: 'success',
      })
    },
  })
}

// 切换收藏状态
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  uni.showToast({
    title: isFavorite.value ? '已收藏' : '已取消收藏',
    icon: 'success',
  })
}

// 预览图片
const previewImage = (image: string) => {
  uni.previewImage({
    urls: roomData.value.images,
    current: image,
  })
}

// 预约看房
const reserveRoom = () => {
  uni.showModal({
    title: '预约看房',
    content: '是否确认预约看房？我们将尽快联系您安排看房时间。',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '预约成功',
          icon: 'success',
        })
        // TODO: 调用预约API
      }
    },
  })
}

// 加载房屋详情数据
const loadRoomDetail = async (roomId: string) => {
  try {
    // TODO: 调用API获取房屋详情
    console.log('加载房屋详情:', roomId)
  } catch (error) {
    console.error('加载房屋详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    loadRoomDetail(options.id)
  }
})
</script>

<style lang="scss" scoped>
.room-detail-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

// 房屋图片轮播
.room-images {
  width: 100%;
  height: 500rpx;

  .image-swiper {
    width: 100%;
    height: 100%;

    .room-image {
      width: 100%;
      height: 100%;
    }
  }
}

// 房屋基本信息
.room-info {
  padding: 30rpx;
  border-bottom: 22rpx solid #f8f8f8;

  .room-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #000000;
  }

  .room-details {
    display: flex;
    align-items: center;
    margin-top: 20rpx;

    .detail-item {
      font-size: 26rpx;
      color: #808080;
    }

    .detail-separator {
      font-size: 26rpx;
      color: #808080;
      margin: 0 20rpx;
    }
  }

  .room-price {
    display: flex;
    align-items: baseline;
    margin-top: 28rpx;
    color: #8cc224;

    .price {
      font-size: 32rpx;
      font-weight: bold;
    }

    .price-unit {
      font-size: 24rpx;
    }

    .price-note {
      font-size: 26rpx;
      color: #8cc224;
      margin-left: 4rpx;
    }
  }

  .room-address {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    margin-top: 16rpx;
    background: #f7f7f7;
    box-sizing: border-box;
    border-radius: 8rpx;

    .location-icon {
      width: 26rpx;
      height: 26rpx;
    }

    .address-text {
      flex: 1;
      font-size: 26rpx;
      color: #000000;
      margin-left: 18rpx;
      @include text-overflow(1);
    }
  }
}

// 房源设施
.facilities-section {
  padding: 30rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #000000;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30rpx;
    margin-top: 40rpx;

    .facility-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .facility-icon {
        width: 60rpx;
        height: 60rpx;
        margin-bottom: 14rpx;
      }

      .facility-name {
        font-size: 24rpx;
        color: #4d4d4d;
        text-align: center;
      }
    }
  }
}

// 房源描述
.description-section {
  margin-top: 20rpx;
  padding: 30rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #000000;
    margin-bottom: 36rpx;
  }

  .description-item {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .description-subtitle {
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;
      margin-bottom: 20rpx;
    }

    .description-list {
      .description-point {
        font-size: 26rpx;
        color: #4d4d4d;
        line-height: 1.6;
        margin-bottom: 10rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .description-content {
      font-size: 26rpx;
      color: #666666;
      line-height: 1.6;
    }
  }
}

.phone-btn {
  width: 110rpx;
  height: 110rpx;
  position: fixed;
  bottom: 270rpx;
  right: 20rpx;
  z-index: 10;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #ffffff;
  box-shadow: 0rpx -1rpx 4rpx 1rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  padding-left: 50rpx;
  padding-right: 30rpx;
  z-index: 100;
  box-sizing: content-box;

  .action-left {
    display: flex;
    align-items: center;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 60rpx;

      .action-icon {
        width: 44rpx;
        height: 44rpx;
        margin-bottom: 6rpx;
      }

      .action-text {
        font-size: 22rpx;
        color: #333333;
      }
    }
  }

  .reserve-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    background: #00a8cf;
    font-size: 28rpx;
    color: #f8f8f8;
    text-align: center;
    border-radius: 8rpx;
  }
}

// 安全区域适配
.bottom-actions {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
