<template>
  <view class="home-page">
    <CustomNavbar v-bind="navState" />

    <view class="home-page-banner">
      <swiper
        class="home-page-banner-swiper"
        :current="bannerState.current"
        :indicator-dots="false"
        :autoplay="true"
        :interval="3000"
        :circular="true"
        @change="onSwiperChange"
      >
        <swiper-item v-for="(item, index) in bannerState.imgList" :key="index">
          <image class="home-page-banner-img" mode="aspectFill" :src="item.picUrl" />
        </swiper-item>
      </swiper>

      <view class="dots">
        <view
          class="dot"
          :class="{ active: bannerState.current === index }"
          v-for="(_, index) in bannerState.imgList"
          :key="index"
        ></view>
      </view>

      <!-- 搜索区域 -->
      <view class="search-section">
        <view class="search-section-l">
          <view class="search-picker">
            <!-- <picker @change="ChooseCity" :range="region" range-key="city" :value="indexCity">
              <view class="picker">{{ region[indexCity].city }}</view>
            </picker> -->
          </view>
          <image class="icon" src="@/static/icon/search-down.png"></image>
        </view>
        <view class="search-section-r" @click="goSearch">
          <image class="icon" src="@/static/icon/search-icon.png" />
          <text>请输入小区名称开始找房</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 推荐门店 -->
      <view class="recommend">
        <view class="recommend-header">
          <text class="recommend-title">推荐门店</text>
          <image class="right-icon" src="@/static/icon/right-home.png"></image>
        </view>

        <swiper class="store-swiper" :indicator-dots="false" :autoplay="false" next-margin="52rpx">
          <swiper-item v-for="(store, index) in storeList" :key="index" class="swiper-item">
            <view class="store-card">
              <view class="store-images">
                <image :src="store.mainImage" class="main-image" mode="aspectFill" />
                <view class="sub-images">
                  <image
                    v-for="(img, imgIndex) in store.subImages"
                    :key="imgIndex"
                    :src="img"
                    class="sub-image"
                    mode="aspectFill"
                  />
                </view>
              </view>

              <view class="store-info">
                <view class="store-details">
                  <text class="store-name">{{ store.name }}</text>
                  <view class="price-info">
                    <text class="price"><text class="price-unit">¥</text>{{ store.price }}</text>
                    <text class="price-unit">/月起</text>
                  </view>
                </view>
                <view class="store-bottom">
                  <image class="icon" src="@/static/icon/address-icon.png"></image>
                  <text class="store-address">{{ store.address }}</text>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 精选房间 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">推荐门店</text>
          <image class="right-icon" src="@/static/icon/right-home.png"></image>
        </view>

        <view class="room-list">
          <view class="room-item" v-for="(room, index) in roomList" :key="index">
            <RoomCard :room-data="room" />
          </view>
        </view>
      </view>
    </view>
    <Tabbar />
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import amapFile from '@/utils/amap-wx';

import CustomNavbar from '@/components/navbar/index.vue'
import RoomCard from '@/components/room-card/index.vue'
import Tabbar from '@/components/tabbar/index.vue'

import { onLoad, onPageScroll, onShareAppMessage } from '@dcloudio/uni-app'

const scrollTop = ref(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
})

const navState = computed(() => ({
  title: '建悦里租房',
  fixed: true,
  showBack: false,
  titleColor: scrollTop.value > 10 ? '#000000' : '#ffffff',
  background: scrollTop.value > 10 ? '#ffffff' : 'linear-gradient( 180deg, #8CC224 0%, rgba(140,194,36,0) 100%)',
}))

const state = reactive({
  mendianList: [],
  // 当前城市ID
  cityId:"",
  // 城市列表
  region: [], 
  // 默认城市
  indexCity:0,
  // banner图
  imgUrls: [],
  address:'',//物理位置
})

let amapKey;

const getGaodeLocation = () => {
   uni.getLocation({
        type: 'wgs84',
        success: function (res){
            //初始化
            amapKey = new amapFile.AMapWX({
                key:'adfdec5d86b3ef1b2f4a74b3d2781587'
            })
            amapKey.getRegeo({
                success:function(e){
                    var address= e[0].regeocodeData.addressComponent.province;
                    getCityList(that,address)
                    getBanner(that)
                },
                fail(){
                    console.log('失败')
                }
            })
        },
        fail(){
            getCityList(that)
            getBanner(that)
        }
    })
}

onLoad(() => {
  getGaodeLocation()
})

// banner
const bannerState = reactive({
  current: 0,
  imgList: [
    {
      picUrl: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    },
  ],
})
const onSwiperChange = (e) => {
  bannerState.current = e.detail.current
}

// 门店列表
const storeList = ref([
  {
    id: 1,
    name: '菊园社区',
    address: '杨浦区国权北路1688弄',
    price: '1980',
    mainImage: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    subImages: [
      'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
      'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    ],
  },
  {
    id: 2,
    name: '绿地社区',
    address: '浦东新区张江路2000弄',
    price: '2200',
    mainImage: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    subImages: [
      'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
      'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    ],
  },
  {
    id: 3,
    name: '万科城市花园',
    address: '徐汇区漕河泾开发区',
    price: '2800',
    mainImage: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    subImages: [
      'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
      'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    ],
  },
])

// 房间列表
const roomList = ref([
  {
    id: 1,
    title: '整租·精品一居·菊园社区',
    area: '28.9',
    floor: 9,
    totalFloor: 11,
    orientationName: '南',
    address: '上海市嘉定区祁连路1255号',
    price: '1550',
    image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    tag: '保租房',
  },
  {
    id: 2,
    title: '整租·精品一居·菊园社区',
    area: '28.9',
    floor: 9,
    totalFloor: 11,
    orientationName: '南',
    address: '上海市嘉定区祁连路1255号',
    price: '1550',
    image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    tag: '保租房',
  },
])

// 跳转到搜索页面
const goSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 加载数据
const loadData = async () => {
  try {
    // TODO: 调用API获取门店和房间数据
    console.log('加载首页数据')
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 页面加载时执行
onMounted(() => {
  loadData()
})

onShareAppMessage(() => {
  return {
    path: '/pages/index/index',
  }
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;

  &-banner {
    width: 100%;
    position: relative;

    &-swiper,
    &-img {
      width: 100%;
      height: 480rpx;
    }

    .dots {
      position: absolute;
      bottom: 38rpx;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      z-index: 9;
      .dot {
        width: 10rpx;
        height: 10rpx;
        background: rgba(255, 255, 255, 0.32);
        margin-right: 8rpx;
        border-radius: 50%;
        &:last-of-type {
          margin-right: 0;
        }
        &.active {
          background: #ffffff;
        }
      }
    }

    .search-section {
      width: 690rpx;
      height: 80rpx;
      position: absolute;
      bottom: -48rpx;
      left: 30rpx;
      font-size: 26rpx;
      box-sizing: border-box;
      background: #fff;
      z-index: 9;
      border-radius: 44rpx;
      border: 1rpx solid #dbdbdb;
      display: flex;
      align-items: center;
      &-l {
        width: 150rpx;
        font-size: 26rpx;
        line-height: 80rpx;
        .search-picker {
          float: left;
          height: 80rpx;
          font-size: 26rpx;
          line-height: 80rpx;
          padding-left: 30rpx;
          .picker {
            max-width: 80rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .icon {
          width: 16rpx;
          height: 10rpx;
          float: left;
          margin: 35rpx 0 0 10rpx;
        }
      }
      &-r {
        flex: 1;
        height: 80rpx;
        margin-left: 30rpx;
        position: relative;
        display: flex;
        align-items: center;
        &::before {
          content: '';
          position: absolute;
          width: 1rpx;
          height: 40rpx;
          background: #dbdbdb;
          top: 20rpx;
          left: -30rpx;
        }
        .icon {
          width: 26rpx;
          height: 26rpx;
        }
        text {
          display: block;
          font-size: 26rpx;
          color: #999999;
          margin-left: 22rpx;
        }
      }
    }
  }
}

.main-content {
  margin-top: 96rpx;

  .recommend {
    &-header {
      padding-left: 30rpx;
      padding-right: 20rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-title {
      font-weight: bold;
      font-size: 32rpx;
      color: #000000;
    }
    .right-icon {
      width: 26rpx;
      height: 26rpx;
    }
    .store-swiper {
      width: 100%;
      height: 440rpx;
      padding-left: 30rpx;
      box-sizing: border-box;
      margin-top: 24rpx;

      .swiper-item {
        padding-right: 20rpx;
        .store-card {
          width: 100%;
          height: 100%;

          .store-images {
            display: flex;
            height: 300rpx;

            .main-image {
              width: 460rpx;
              height: 100%;
              border-radius: 16rpx;
            }

            .sub-images {
              width: 200rpx;
              margin-left: 20rpx;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              .sub-image {
                width: 200rpx;
                height: 140rpx;
                border-radius: 16rpx;
              }
            }
          }

          .store-info {
            width: 100%;
            margin-top: 20rpx;

            .store-details {
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .store-name {
                font-weight: bold;
                font-size: 28rpx;
                color: #000000;
              }

              .price-info {
                display: flex;
                align-items: baseline;
                gap: 5rpx;
                color: #8cc224;

                .price {
                  font-size: 36rpx;
                  font-weight: bold;
                }

                .price-unit {
                  font-size: 24rpx;
                }
              }
            }
          }

          .store-bottom {
            display: flex;
            align-items: center;
            margin-top: 20rpx;

            .icon {
              width: 24rpx;
              height: 24rpx;
            }

            .store-address {
              font-size: 24rpx;
              color: #666666;
              display: block;
              margin-left: 12rpx;
            }
          }
        }
      }
    }
  }
}

.section {
  margin-top: 50rpx;

  &-header {
    padding-left: 30rpx;
    padding-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-title {
    font-weight: bold;
    font-size: 32rpx;
    color: #000000;
  }

  .right-icon {
    width: 26rpx;
    height: 26rpx;
  }

  .room-list {
    padding: 30rpx;
    .room-item {
      margin-bottom: 30rpx;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
</style>
