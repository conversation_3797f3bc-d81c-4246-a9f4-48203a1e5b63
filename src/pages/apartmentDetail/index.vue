<template>
  <view class="room-detail-page">
    <!-- 房屋图片轮播 -->
    <view class="room-images">
      <swiper
        class="image-swiper"
        :indicator-dots="true"
        autoplay
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#ffffff"
      >
        <swiper-item v-for="(image, index) in roomData.images" :key="index">
          <image :src="image" class="room-image" mode="aspectFill" @click="previewImage(image)" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 房屋基本信息 -->
    <view class="room-info">
      <view class="room-title">{{ roomData.title }}</view>
      <view class="room-price">
        <text class="price">{{ roomData.price }}</text>
        <text class="price-unit">元/月起</text>
        <text class="price-note">（以实际签约为准）</text>
      </view>
      <view class="room-address">
        <image class="location-icon" src="@/static/icon/address-icon.png" />
        <text class="address-text">{{ roomData.address }}</text>
      </view>
      <view class="room-detail">
        <view class="room-detail-item">电梯</view>
        <view class="room-detail-item">便利店</view>
        <view class="room-detail-item">停车场</view>
        <view class="room-detail-item">安全监控</view>
      </view>
    </view>

    <!-- 房型介绍 -->
    <view class="room-types-section">
      <view class="section-header">
        <view class="section-title">房型介绍</view>
        <view class="view-all" @click="viewAllRoomTypes">
          <text class="view-all-text">查看全部</text>
          <image class="arrow-icon" src="@/static/icon/right-home.png" />
        </view>
      </view>

      <view class="room-types-list">
        <view
          v-for="(roomType, index) in roomTypes"
          :key="index"
          class="room-type-item"
          @click="viewRoomTypeDetail(roomType)"
        >
          <image :src="roomType.image" class="room-type-image" mode="aspectFill" />
          <view class="room-type-info">
            <view class="room-type-name">{{ roomType.name }}</view>
            <view class="room-type-details">
              <text class="detail-text">{{ roomType.area }}㎡</text>
              <text class="detail-separator">|</text>
              <text class="detail-text">{{ roomType.layout }}</text>
            </view>
            <view class="room-type-price">
              <text class="price">{{ roomType.price }}</text>
              <text class="price-unit">元/月起</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 周边及交通 -->
    <view class="nearby-section">
      <view class="section-header">
        <view class="section-title">周边及交通</view>
        <view class="view-all" @click="viewAllNearby">
          <text class="view-all-text">查看全部</text>
          <image class="arrow-icon" src="@/static/icon/right-home.png" />
        </view>
      </view>

      <view class="nearby-content">
        <view class="nearby-intro">
          <text class="intro-text">周边介绍：</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-item" @click="toggleFavorite">
          <image v-if="isFavorite" src="@/static/icon/heart-filled.png" class="action-icon" />
          <image v-else src="@/static/icon/heart-outline.png" class="action-icon" />
          <text class="action-text">收藏</text>
        </view>
        <view class="action-item" @click="shareRoom">
          <image src="@/static/icon/share-icon.png" class="action-icon" />
          <text class="action-text">分享</text>
        </view>
      </view>
      <view class="reserve-btn"> 电话咨询 </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

// 房屋数据接口
interface RoomData {
  id: number
  title: string
  area: string
  floor: number
  totalFloor: number
  orientation: string
  price: string
  address: string
  images: string[]
  advantages: string[]
  layoutDescription: string
}

// 房型接口
interface RoomType {
  name: string
  area: string
  layout: string
  price: string
  image: string
}

// 周边分类接口
interface NearbyCategory {
  name: string
  items: NearbyItem[]
}

// 周边项目接口
interface NearbyItem {
  name: string
  distance: string
  type: string
}

// 响应式数据
const roomData = ref<RoomData>({
  id: 1,
  title: '整租·菊园社区三居室',
  area: '72',
  floor: 2,
  totalFloor: 19,
  orientation: '朝南',
  price: '1550',
  address: '上海市嘉定区祁连路1255号',
  images: [
    'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
    'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
  ],
  advantages: ['满满国际户型，2房全明格局', '客厅宽敞大气', '装修状况：中装修提包入住'],
  layoutDescription: '户型：2室2厅1卫，南北通透房为正使用率高达90%',
})

const isFavorite = ref(false)

// 房型数据
const roomTypes = ref<RoomType[]>([
  {
    name: '房型名称',
    area: '1室0厅',
    layout: '装修类型名称',
    price: '0',
    image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
  },
  {
    name: '房型名称',
    area: '1室0厅',
    layout: '装修类型名称',
    price: '0',
    image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
  },
])

// 分享房源
const shareRoom = () => {
  uni.showActionSheet({
    itemList: ['微信好友', '朋友圈', '复制链接'],
    success: (res) => {
      const actions = ['微信好友', '朋友圈', '复制链接']
      uni.showToast({
        title: `分享到${actions[res.tapIndex]}`,
        icon: 'success',
      })
    },
  })
}

// 切换收藏状态
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  uni.showToast({
    title: isFavorite.value ? '已收藏' : '已取消收藏',
    icon: 'success',
  })
}

// 预览图片
const previewImage = (image: string) => {
  uni.previewImage({
    urls: roomData.value.images,
    current: image,
  })
}

// 查看全部房型
const viewAllRoomTypes = () => {
  uni.showToast({
    title: '查看全部房型',
    icon: 'none',
  })
}

// 查看房型详情
const viewRoomTypeDetail = (roomType: RoomType) => {
  uni.showToast({
    title: `查看${roomType.name}详情`,
    icon: 'none',
  })
}

// 查看全部周边
const viewAllNearby = () => {
  uni.showToast({
    title: '查看全部周边',
    icon: 'none',
  })
}

// 加载房屋详情数据
const loadRoomDetail = async (roomId: string) => {
  try {
    // TODO: 调用API获取房屋详情
    console.log('加载房屋详情:', roomId)
  } catch (error) {
    console.error('加载房屋详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.id) {
    loadRoomDetail(options.id)
  }
})
</script>

<style lang="scss" scoped>
.room-detail-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

// 房屋图片轮播
.room-images {
  width: 100%;
  height: 500rpx;

  .image-swiper {
    width: 100%;
    height: 100%;

    .room-image {
      width: 100%;
      height: 100%;
    }
  }
}

// 房屋基本信息
.room-info {
  padding: 30rpx;
  border-bottom: 22rpx solid #f8f8f8;

  .room-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #000000;
  }

  .room-price {
    display: flex;
    align-items: baseline;
    margin-top: 28rpx;
    color: #8cc224;

    .price {
      font-size: 32rpx;
      font-weight: bold;
    }

    .price-unit {
      font-size: 24rpx;
    }

    .price-note {
      font-size: 26rpx;
      color: #8cc224;
      margin-left: 4rpx;
    }
  }

  .room-address {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    margin-top: 16rpx;
    background: #f7f7f7;
    box-sizing: border-box;
    border-radius: 8rpx;

    .location-icon {
      width: 26rpx;
      height: 26rpx;
    }

    .address-text {
      flex: 1;
      font-size: 26rpx;
      color: #000000;
      margin-left: 18rpx;
      @include text-overflow(1);
    }
  }

  .room-detail {
    margin-top: 16rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    &-item {
      background: #f8f8f8;
      border-radius: 4rpx;
      padding: 0 10rpx;
      font-size: 22rpx;
      color: #666666;
      height: 36rpx;
      line-height: 36rpx;
    }
  }
}

// 房型介绍
.room-types-section {
  padding: 30rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #000000;
    }

    .view-all {
      display: flex;
      align-items: center;

      .view-all-text {
        font-size: 22rpx;
        color: #666666;
        margin-right: 10rpx;
      }

      .arrow-icon {
        width: 26rpx;
        height: 26rpx;
      }
    }
  }

  .room-types-list {
    margin-top: 26rpx;
    display: flex;
    justify-content: space-between;
    .room-type-item {
      display: flex;
      flex-direction: column;
      background-color: #ffffff;
      width: 336rpx;

      .room-type-image {
        width: 100%;
        height: 240rpx;
        border-radius: 8rpx;
      }

      .room-type-info {
        flex: 1;
        padding: 16rpx 10rpx;

        .room-type-name {
          font-size: 28rpx;
          font-weight: bold;
          color: #000000;
          @include text-overflow(1);
        }

        .room-type-details {
          display: flex;
          align-items: center;
          margin-top: 10rpx;
          @include text-overflow(1);

          .detail-text {
            font-size: 22rpx;
            color: #808080;
          }

          .detail-separator {
            font-size: 22rpx;
            color: #808080;
            margin: 0 15rpx;
          }
        }

        .room-type-price {
          display: flex;
          align-items: baseline;
          margin-top: 10rpx;

          .price {
            font-size: 32rpx;
            font-weight: bold;
            color: #8cc224;
          }

          .price-unit {
            font-size: 24rpx;
            color: #8cc224;
          }
        }
      }
    }
  }
}

// 周边及交通
.nearby-section {
  padding: 30rpx;
  background-color: #ffffff;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #000000;
    }

    .view-all {
      display: flex;
      align-items: center;

      .view-all-text {
        font-size: 22rpx;
        color: #666666;
        margin-right: 10rpx;
      }

      .arrow-icon {
        width: 26rpx;
        height: 26rpx;
      }
    }
  }

  .nearby-content {
    margin-top: 26rpx;
    .nearby-intro {
      margin-bottom: 20rpx;

      .intro-text {
        font-size: 26rpx;
        color: #4d4d4d;
      }
    }
  }
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #ffffff;
  box-shadow: 0rpx -1rpx 4rpx 1rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  padding-left: 50rpx;
  padding-right: 30rpx;
  z-index: 100;
  box-sizing: content-box;

  .action-left {
    display: flex;
    align-items: center;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 60rpx;

      .action-icon {
        width: 44rpx;
        height: 44rpx;
        margin-bottom: 6rpx;
      }

      .action-text {
        font-size: 22rpx;
        color: #333333;
      }
    }
  }

  .reserve-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    background: #00a8cf;
    font-size: 28rpx;
    color: #f8f8f8;
    text-align: center;
    border-radius: 8rpx;
  }
}

// 安全区域适配
.bottom-actions {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
