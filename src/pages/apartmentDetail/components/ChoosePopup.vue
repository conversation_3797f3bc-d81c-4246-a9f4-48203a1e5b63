<template>
  <uni-popup ref="popupRef" type="top" @touchmove.stop.prevent :is-mask-click="true" @maskClick="close">
    <view class="choose-popup" :style="{ paddingTop: top + 'px' }">
      <view class="choose-area">
        <text
          class="choose-item"
          :class="{ active: item.value === currentValue }"
          v-for="(item, index) in chooseList"
          :key="index"
          @click="chooseItem(item.value)"
          >{{ item.label }}</text
        >
      </view>
      <view class="choose-bottom">
        <view class="choose-bottom-reset" @click="onReset">重置</view>
        <view class="choose-bottom-confirm" @click="onConfirm">确定</view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

const props = defineProps<{
  top: number
  setionList: any[]
}>()

const emits = defineEmits(['confirm'])

const chooseList = computed(() => props.setionList.find((v) => v.isCurrent)?.list)

const currentValue = computed(() => props.setionList.find((v) => v.isCurrent)?.chooseValue)

const chooseItem = (value) => {
  props.setionList.find((v) => v.isCurrent).chooseValue = value
}

const onReset = () => {
  props.setionList.forEach((v) => {
    v.chooseValue = ''
  })
}

const onConfirm = () => {
  close()
  emits('confirm')
}

const close = () => {
  props.setionList.forEach((v) => {
    v.isCurrent = false
  })
  popupRef.value.close()
}

const open = () => {
  popupRef.value.open()
}

const popupRef = ref()
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.choose-popup {
  width: 100%;
  background: #ffffff;
  border-radius: 0rpx 0rpx 30rpx 30rpx;
  padding: 0 30rpx 40rpx;
  box-sizing: border-box;
  .choose-area {
    width: 100%;
    height: 330rpx;
    padding: 30rpx 0 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    .choose-item {
      font-size: 28rpx;
      color: #333333;
      text-align: center;
      &.active {
        color: #8cc224;
      }
    }
  }
  .choose-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-reset {
      width: 240rpx;
      height: 80rpx;
      background: #ffffff;
      border-radius: 8rpx;
      border: 1rpx solid #00a8cf;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #00a8cf;
    }
    &-confirm {
      width: 420rpx;
      height: 80rpx;
      background: #00a8cf;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #f8f8f8;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
