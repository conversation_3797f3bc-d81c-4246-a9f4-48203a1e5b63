<template>
  <view class="mine-page">
    <!-- 背景 -->
    <image class="mine-background" :src="`${imgUrl}/mine-bg.png`"></image>

    <view class="mine-page-container">
      <view class="mine-page-container-top">
        <!-- 自定义导航栏 -->
        <CustomNavbar title="我的" :show-back="false" background="transparent" />

        <!-- 用户信息区域 -->
        <view class="user-section">
          <view class="user-info">
            <view class="user-details">
              <text class="phone-number">{{ userInfo.nickname || userInfo.phone || '请登录' }}</text>
              <text class="user-desc">注册手机号不会透漏给别人</text>
            </view>
            <image v-if="userInfo.avatar" class="avatar" :src="userInfo.avatar"></image>
            <image v-else class="avatar" :src="`${imgUrl}/mine-avatar.png`"></image>
          </view>

          <!-- 统计信息 -->
          <view class="stats-section">
            <view class="stat-item" v-for="item in state.statsList" :key="item.key" @click="handleClick(item, 'stats')">
              <image class="stat-icon" :src="item.icon"></image>
              <text class="stat-text">{{ item.name }}({{ item.num }})</text>
            </view>
          </view>
        </view>
      </view>

      <view class="mine-page-container-bottom">
        <!-- banner -->
        <view class="mine-banner">
          <image class="mine-banner-img" mode="widthFix" :src="`${imgUrl}/mine-banner.png`"></image>
        </view>

        <!-- 我的家功能区 -->
        <view class="my-home-section">
          <view class="section-header">
            <text class="section-title">我的家()</text>
            <view class="switch-source" @click="switchSource">
              <text class="switch-text">切换房源</text>
              <image class="switch-arrow" src="@/static/icon/right.png"></image>
            </view>
          </view>

          <!-- 功能网格 -->
          <view class="function-grid">
            <view
              class="function-item"
              v-for="item in state.functionList"
              :key="item.key"
              @click="handleClick(item, 'function')"
            >
              <image class="function-icon" :src="item.icon"></image>
              <text class="function-text">{{ item.name }}</text>
            </view>
          </view>
        </view>

        <!-- 退出登录按钮 -->
        <view class="logout-section">
          <button class="logout-btn" @click="handleLogout">退出登录</button>
        </view>
      </view>
    </view>
    <Tabbar />
  </view>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { reactive, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'
import Tabbar from '@/components/tabbar/index.vue'

import { getAppointmentNum, getCollectNum, getPaybill, getReserveNum, getUserHouseInfo } from '@/apis/user'
import { userStore } from '@/store'
import { imgUrl } from '@/utils/request'
import { clearStorage, setItemObj } from '@/utils/storage'
import { onShow } from '@dcloudio/uni-app'

const store = userStore()
const { userInfo } = storeToRefs(store)

const state = reactive({
  // 房间列表
  roomList: [],
  // 是否弹出房间信息和遮罩
  showModel: false,
  // 选择房间
  chooseRoom: true,
  // 选中的房间信息
  chooseRoomInfo: [],
  // 房间编号
  ischeck: 0,
  // 是否弹出我的管家弹框
  guanjiaModel: false,
  // 管家信息
  houseKeeper: [],
  //房间信息
  userHouseInfo: [],
  // 用户电话
  phone: '',
  // 用户昵称
  nickname: '',
  // 渲染我的房间名称
  userRoomShow: '',
  // 7天内代缴账单提示
  remark: false,
  // 是否有账单
  isPayBill: false,
  //记录一个下标判断当前合同
  index: 0,
  //线上签约未签约的合同
  xsqyNoOk: [],
  statsList: [
    {
      key: 'collection',
      name: '收藏',
      num: 0,
      icon: `${imgUrl}/mine-collection.png`,
      url: '/userCenter/collection/index',
    },
    {
      key: 'appointment',
      name: '约看',
      num: 0,
      icon: `${imgUrl}/mine-appointment.png`,
      url: '/userCenter/appointment/index',
    },
    {
      key: 'reserve',
      name: '预定',
      num: 0,
      icon: `${imgUrl}/mine-reserve.png`,
      url: '/userCenter/reserve/index',
    },
  ],
  functionList: [
    {
      key: 'contract',
      name: '合同',
      icon: `${imgUrl}/mine-contract.png`,
      url: '/userCenter/contract/index',
    },
    {
      key: 'bill',
      name: '账单',
      icon: `${imgUrl}/mine-bill.png`,
      url: '/userCenter/bill/index',
    },
    {
      key: 'lock',
      name: '智能锁',
      icon: `${imgUrl}/mine-lock.png`,
      url: '/userCenter/lock/index',
    },
    {
      key: 'access',
      name: '门禁',
      icon: `${imgUrl}/mine-access.png`,
      url: '/userCenter/access/index',
    },
    {
      key: 'butler',
      name: '管家',
      icon: `${imgUrl}/mine-butler.png`,
      url: '/userCenter/butler/index',
    },
    {
      key: 'maintenance',
      name: '维修',
      icon: `${imgUrl}/mine-maintenance.png`,
      url: '/userCenter/maintenance/index',
    },
    {
      key: 'cleaning',
      name: '保洁',
      icon: `${imgUrl}/mine-cleaning.png`,
      url: '/userCenter/cleaning/index',
    },
    {
      key: 'complaint',
      name: '投诉',
      icon: `${imgUrl}/mine-complaint.png`,
      url: '/userCenter/complaint/index',
    },
  ],
})

const handleClick = (item, type: string) => {
  if (!userInfo.value.phone) {
    uni.navigateTo({
      url: '/pages/login/index',
    })
    return
  }
  if (!state.roomList.length && type === 'function') {
    uni.showToast({
      title: '还没有您的房间信息',
      icon: 'none',
    })
    return
  }
  if (item.url) {
    uni.navigateTo({
      url: item.url,
    })
  }
}

// 切换房源
const switchSource = () => {
  if (!state.roomList.length) {
    uni.showToast({
      title: '还没有您的房间信息',
      icon: 'none',
    })
    return
  }
  state.showModel = true
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        clearStorage()
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
        })

        // 跳转到登录页面
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/login/index',
          })
        }, 500)
      }
    },
  })
}

// 获取待缴账单列表
const loadPaybill = async () => {
  const res = await getPaybill(
    {
      phone: state.chooseRoomInfo.zukePhone,
      indentChengzuId: state.chooseRoomInfo.id,
      indentType: 1,
    },
    '加载中',
  )
  const list = res?.list || []
  state.isPayBill = list.length > 0
  state.remark = list.some((item) => {
    const predictTime = new Date(item.predictTime)
    const nowTime = new Date()
    const diff = predictTime.getTime() - nowTime.getTime()
    return diff < 1000 * 60 * 60 * 24 * 7
  })
}

// 获取用户相关信息
const loadUserHouseInfo = async () => {
  try {
    const res = await getUserHouseInfo({
      zukePhone: userInfo.value.phone,
    })

    const userHouseInfo = res?.list || []

    const userHouseInfoList = userHouseInfo.filter((item) => item.status == 0 && item.contractAuditStatus == 2)

    if (userHouseInfoList.length) {
      state.userHouseInfo = userHouseInfoList
      state.roomList = userHouseInfoList
      state.chooseRoomInfo = userHouseInfoList[state.ischeck]
      state.userRoomShow = userHouseInfoList[state.ischeck].house.quyuCName //获取下标0

      setItemObj('userHouseInfo', userHouseInfo)
      setItemObj('chooseRoomInfo', userHouseInfoList[state.ischeck])

      loadPaybill()
    }

    //获取待签约的合同
    state.xsqyNoOk = userHouseInfo.filter(
      (item) =>
        item.isElectron == 1 &&
        ((item.status == 20 && item.isAudited == 0) ||
          (item.status == 20 && item.isAudited == 1 && item.signingStatus == 1)),
    )
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取收藏数量
const loadCollectionNum = async () => {
  try {
    const res = await getCollectNum({
      phone: userInfo.value.phone,
    })
    const list = res?.houseArr || []
    const oList = list.filter((item) => item.id !== '' && item.id != null)
    const item = state.statsList.find((item) => item.key === 'collection')!
    item.num = oList.length
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取约看数量
const loadAppointmentNum = async () => {
  try {
    const res = await getAppointmentNum({
      phone: userInfo.value.phone,
    })
    const list = res?.renterInfoArr || []
    const oList = list.filter((item) => item.houseType !== '' && item.houseType != null)
    const item = state.statsList.find((item) => item.key === 'appointment')!
    item.num = oList.length
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取预定数量
const loadReserveNum = async () => {
  try {
    const res = await getReserveNum({
      zukePhone: userInfo.value.phone,
    })
    const list = res?.list || []
    const oList = list.filter((item) => item.houseType !== '' && item.houseType != null)
    const item = state.statsList.find((item) => item.key === 'reserve')!
    item.num = oList.length
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    console.log('加载统计数据')
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onShow(() => {
  if (userInfo.value.phone) {
    loadUserHouseInfo()
    loadCollectionNum()
    loadAppointmentNum()
    loadReserveNum()
  }
})
</script>

<style lang="scss" scoped>
.mine-page {
  min-height: 100vh;
  background-color: #ffffff;
  position: relative;

  .mine-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 600rpx;
    z-index: 1;
  }

  &-container {
    position: relative;
    z-index: 2;

    &-bottom {
      background: #ffffff;
      border-radius: 20rpx 20rpx 0rpx 0rpx;
      margin-top: 30rpx;
      padding-top: 30rpx;
    }
  }
}

.user-section {
  margin-top: 60rpx;
  padding-left: 30rpx;
  padding-right: 40rpx;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .user-details {
    flex: 1;

    .phone-number {
      display: block;
      font-size: 34rpx;
      font-weight: bold;
      color: #000000;
    }

    .user-desc {
      font-size: 26rpx;
      color: #707070;
      margin-top: 28rpx;
      display: block;
    }
  }

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
  }
}

.stats-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 50rpx;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .stat-icon {
      width: 50rpx;
      height: 50rpx;
    }

    .stat-text {
      font-size: 26rpx;
      color: #707070;
      margin-top: 16rpx;
    }
  }
}

.mine-banner {
  padding: 0 30rpx;

  &-img {
    width: 100%;
  }
}

.my-home-section {
  margin-top: 50rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;

  .section-title {
    font-size: 34rpx;
    font-weight: bold;
    color: #000000;
  }

  .switch-source {
    display: flex;
    align-items: center;

    .switch-text {
      font-size: 24rpx;
      color: #666666;
    }

    .switch-arrow {
      width: 26rpx;
      height: 26rpx;
      margin-left: 4rpx;
    }
  }
}

.function-grid {
  display: flex;
  flex-wrap: wrap;

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40rpx;
    width: 25%;

    .function-icon {
      width: 60rpx;
      height: 60rpx;
    }

    .function-text {
      font-size: 26rpx;
      color: #333333;
      margin-top: 16rpx;
    }
  }
}

.logout-section {
  margin: 70rpx 30rpx;

  .logout-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #00a8cf;
    color: #ffffff;
    border: none;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 400;

    &::after {
      border: none;
    }
  }
}
</style>
