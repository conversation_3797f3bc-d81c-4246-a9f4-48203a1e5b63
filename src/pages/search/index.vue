<template>
  <view class="search-page">
    <!-- 搜索框 -->
    <SearchBar v-model="searchKeyword" @seach="handleSearch" />

    <!-- 历史搜索 -->
    <view v-if="searchHistory.length > 0" class="history-section">
      <view class="history-header">
        <text class="history-title">搜索历史</text>
        <image class="clear-icon" src="@/static/icon/delete-icon.png" @click="clearHistory" />
      </view>
      <view class="history-content">
        <view v-for="(item, index) in searchHistory" :key="index" class="history-item" @click="searchFromHistory(item)">
          {{ item }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

import SearchBar from '@/components/search-bar/index.vue'

// 页面数据
const searchKeyword = ref('')
const searchHistory = ref<string[]>([])

// 执行搜索
const handleSearch = async () => {
  const keyword = searchKeyword.value.trim()
  if (!keyword) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none',
    })
    return
  }

  addToHistory(keyword)
}

// 从历史记录或热门搜索中搜索
const searchFromHistory = (keyword: string) => {
  searchKeyword.value = keyword
  handleSearch()
}

// 添加到搜索历史
const addToHistory = (keyword: string) => {
  // 移除重复项
  const index = searchHistory.value.indexOf(keyword)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }

  // 添加到开头
  searchHistory.value.unshift(keyword)

  // 限制历史记录数量
  if (searchHistory.value.length > 5) {
    searchHistory.value = searchHistory.value.slice(0, 5)
  }

  // 保存到本地存储
  uni.setStorageSync('searchHistory', searchHistory.value)
}

// 清除搜索历史
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '是否确定清空搜索历史？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = []
        uni.removeStorageSync('searchHistory')
        uni.showToast({
          title: '已清除',
          icon: 'success',
        })
      }
    },
  })
}

// 加载搜索历史
const loadSearchHistory = () => {
  try {
    const history = uni.getStorageSync('searchHistory')
    if (history && Array.isArray(history)) {
      searchHistory.value = history
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error)
  }
}

// 页面加载时执行
onMounted(() => {
  loadSearchHistory()
})
</script>

<style lang="scss" scoped>
.search-page {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  min-height: 100vh;
  background-color: #ffffff;
}

// 历史搜索
.history-section {
  margin-top: 38rpx;

  .history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .history-title {
      font-size: 28rpx;
      color: #000000;
    }

    .clear-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .history-content {
    display: flex;
    flex-wrap: wrap;

    .history-item {
      padding: 0 30rpx;
      height: 66rpx;
      line-height: 66rpx;
      font-size: 26rpx;
      color: #4d4d4d;
      background: #f8f8f8;
      border-radius: 16rpx;
      margin-right: 20rpx;
      margin-top: 28rpx;
    }
  }
}
</style>
