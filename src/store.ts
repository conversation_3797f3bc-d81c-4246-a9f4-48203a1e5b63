import { defineStore } from 'pinia'
import { reactive, toRefs } from 'vue'

import { getItemObj, removeItemObj, setItemObj } from './utils/storage'

const USERINFONAME = 'userInfo'

const user = getItemObj(USERINFONAME) || {}

export const userStore = defineStore('user', () => {
  const state = reactive({
    userInfo: {
      ...user,
    },
  })

  const setUserInfo = (info) => {
    state.userInfo = info
    setItemObj(USERINFONAME, info)
  }

  const removeUserInfo = () => {
    state.userInfo = {}
    removeItemObj(USERINFONAME)
  }

  return {
    ...toRefs(state),
    setUserInfo,
    removeUserInfo,
  }
})
